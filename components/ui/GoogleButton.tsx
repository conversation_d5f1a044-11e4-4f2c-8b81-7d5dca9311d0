import FontAwesome from "@expo/vector-icons/FontAwesome";
import { GoogleSignin } from "@react-native-google-signin/google-signin";
import { router } from "expo-router";
import { StyleSheet, Text, TouchableOpacity, View } from "react-native";

import { useAuth } from "@/context/AuthContext";

interface GoogleButtonProps {
  signup?: boolean;
}

export function GoogleButton({ signup = false }: GoogleButtonProps) {
  const { signIn } = useAuth();

  const handleGoogleSignIn = async () => {
    GoogleSignin.configure({
      // webClientId: process.env.EXPO_PUBLIC_GOOGLE_WEB_CLIENT_ID,
      iosClientId: process.env.EXPO_PUBLIC_GOOGLE_IOS_CLIENT_ID,
      offlineAccess: true,
    });

    try {
      const user = await GoogleSignin.signIn({});

      console.log("user", user);

      const response = await fetch(
        process.env.EXPO_PUBLIC_API_DOMAIN + "/auth/signinup",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json; charset=utf-8",
          },
          body: JSON.stringify({
            thirdPartyId: "google",
            redirectURIInfo: {
              redirectURIOnProviderDashboard:
                "http://localhost:8081/auth/callback/google", // this value doesn't matter cause it's mobile login, and Google doesn't check it, but our APIs need some value for it.
              redirectURIQueryParams: {
                code: user.data?.serverAuthCode,
              },
            },
          }),
        }
      );

      const data = await response.json();

      switch (data.status) {
        case "OK":
          console.log("signin successfully");
          await signIn(data.accessToken, data.expiresInSeconds);
          router.replace("/(app)/home");
          break;
        case "NO_EMAIL_GIVEN_BY_PROVIDER":
          console.error("No email provided by Google");
          // Handle case where provider didn't provide an email
          // You might want to show a message to the user to try another sign-in method
          break;
        case "SIGN_IN_UP_NOT_ALLOWED":
          console.error("Sign in/up not allowed", data.reason);
          // The reason prop contains a support code explaining why sign in/up was not allowed
          // This can happen during automatic account linking or during MFA
          break;
        case "GENERAL_ERROR":
          console.error("General error");
          // This happens if you've overridden the backend API to send a custom error message
          break;
        default:
          console.error("Unexpected error:", data);
      }
    } catch (e) {
      console.error("Google sign in failed with error", JSON.stringify(e));
    }
  };

  return (
    <TouchableOpacity style={styles.googleButton} onPress={handleGoogleSignIn}>
      <View style={styles.googleButtonContent}>
        <FontAwesome name="google" size={20} color="#000" />
        <Text style={styles.googleButtonText}>
          {signup ? "Sign up with Google" : "Sign in with Google"}
        </Text>
      </View>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  googleButton: {
    backgroundColor: "#FFFFFF",
    padding: 15,
    borderRadius: 8,
    alignItems: "center",
    marginTop: 24,
  },
  googleButtonContent: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    gap: 8,
  },
  googleButtonText: {
    color: "#000",
    fontSize: 16,
    fontWeight: "600",
  },
});
