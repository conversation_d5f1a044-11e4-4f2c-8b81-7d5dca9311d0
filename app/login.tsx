import { Link, router } from "expo-router";
import React, { useState } from "react";
import {
  Alert,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";

import { Button } from "@/components/ui/Button";
import { Input } from "@/components/ui/Input";
import { Logo } from "@/components/ui/Logo";
import { Colors } from "@/constants/Colors";
import { useAuth } from "@/context/AuthContext";
import { useTranslation } from "@/context/LanguageContext";
import { useColorScheme } from "@/hooks/useColorScheme";
import { useLogin } from "@/lib/api/hooks";

export default function LoginScreen() {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? "light"];
  const { signIn } = useAuth();
  const loginMutation = useLogin();
  const { t } = useTranslation();

  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [rememberMe, setRememberMe] = useState(false);

  const handleLogin = async () => {
    if (!email || !password) {
      Alert.alert(t("common.error"), t("auth.enterEmailPassword"));
      return;
    }

    try {
      const response = await loginMutation.mutateAsync({
        email,
        password,
      });

      await signIn(response.accessToken, response.expiresInSeconds);
      router.replace("/(app)/home");
    } catch (error) {
      Alert.alert(
        t("common.error"),
        error instanceof Error ? error.message : t("auth.loginError")
      );
    }
  };

  const handleSocialLogin = (provider: string) => {
    console.log(`Login with ${provider}`);
    // TODO: Implement social login
  };

  return (
    <SafeAreaView
      style={[styles.container, { backgroundColor: colors.background }]}
    >
      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        style={styles.keyboardView}
      >
        <ScrollView
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          <View style={styles.logoContainer}>
            <Logo width={180} height={27} />
          </View>

          <Text style={[styles.title, { color: colors.text }]}>
            {t("auth.welcomeBack")}
          </Text>
          <Text style={[styles.subtitle, { color: colors.text }]}>
            {t("auth.loginSubtitle")}
          </Text>

          <View style={styles.form}>
            <Input
              label={t("auth.email")}
              placeholder={t("auth.emailPlaceholder")}
              value={email}
              onChangeText={setEmail}
              keyboardType="email-address"
              autoCapitalize="none"
              autoComplete="email"
            />

            <Input
              label={t("auth.password")}
              placeholder={t("auth.passwordPlaceholder")}
              value={password}
              onChangeText={setPassword}
              isPassword
              autoComplete="password"
            />

            {/* <View style={styles.rememberForgotRow}>
              <TouchableOpacity
                style={styles.rememberMe}
                onPress={() => setRememberMe(!rememberMe)}
              >
                <Checkbox value={rememberMe} onValueChange={setRememberMe} />
                <Text style={[styles.rememberMeText, { color: colors.text }]}>
                  {t("auth.rememberMe")}
                </Text>
              </TouchableOpacity>

              <TouchableOpacity>
                <Text style={styles.forgotText}>
                  {t("auth.forgotPassword")}
                </Text>
              </TouchableOpacity>
            </View> */}
          </View>

          <Button
            title={t("auth.login")}
            onPress={handleLogin}
            loading={loginMutation.isPending}
            containerStyle={styles.signInButton}
          />

          <View style={styles.dividerContainer}>
            <View style={[styles.divider, { backgroundColor: colors.text }]} />
            <Text style={[styles.dividerText, { color: colors.text }]}>
              {t("auth.continueWith")}
            </Text>
            <View style={[styles.divider, { backgroundColor: colors.text }]} />
          </View>

          <View style={styles.socialButtons}>
            <GoogleButton signup={false} />
          </View>

          <View style={styles.footer}>
            <Text style={[styles.footerText, { color: colors.text }]}>
              {t("auth.dontHaveAccount")}{" "}
            </Text>
            <Link href="/sign-up" asChild>
              <TouchableOpacity>
                <Text style={styles.linkText}>{t("auth.signup")}</Text>
              </TouchableOpacity>
            </Link>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  keyboardView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    paddingHorizontal: 24,
    paddingVertical: 40,
    justifyContent: "center",
  },
  logoContainer: {
    alignItems: "center",
    marginBottom: 32,
  },
  title: {
    fontSize: 28,
    fontWeight: "700",
    textAlign: "center",
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    textAlign: "center",
    marginBottom: 32,
    opacity: 0.7,
  },
  form: {
    marginBottom: 24,
  },
  rememberForgotRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  rememberMe: {
    flexDirection: "row",
    alignItems: "center",
  },
  rememberMeText: {
    marginLeft: 8,
    fontSize: 14,
  },
  forgotText: {
    fontSize: 14,
    color: "#3b82f6",
  },
  signInButton: {
    marginTop: 8,
  },
  dividerContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginVertical: 24,
  },
  divider: {
    flex: 1,
    height: 1,
    backgroundColor: "#e5e7eb",
  },
  dividerText: {
    paddingHorizontal: 16,
    fontSize: 14,
    color: "#6b7280",
  },
  socialButtons: {
    marginBottom: 24,
  },
  footer: {
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
  },
  footerText: {
    fontSize: 14,
  },
  linkText: {
    fontSize: 14,
    color: "#3b82f6",
    fontWeight: "500",
  },
});
