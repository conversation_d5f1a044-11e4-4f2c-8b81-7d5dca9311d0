import { Link, router } from "expo-router";
import React, { useState } from "react";
import {
  Alert,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";

import { Button } from "@/components/ui/Button";
import { Input } from "@/components/ui/Input";
import { Logo } from "@/components/ui/Logo";
import { Colors } from "@/constants/Colors";
import { useAuth } from "@/context/AuthContext";
import { useTranslation } from "@/context/LanguageContext";
import { useColorScheme } from "@/hooks/useColorScheme";
import { useSignup } from "@/lib/api/hooks";

export default function SignUpScreen() {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? "light"];
  const { signIn } = useAuth();
  const { t } = useTranslation();
  const signupMutation = useSignup();

  const [firstName, setFirstName] = useState("");
  const [lastName, setLastName] = useState("");
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");

  const handleSignUp = async () => {
    if (!firstName || !lastName || !email || !password) {
      Alert.alert(t("common.error"), t("auth.fillAllFields"));
      return;
    }

    if (password !== confirmPassword) {
      Alert.alert(t("common.error"), t("auth.passwordMismatch"));
      return;
    }

    try {
      const response = await signupMutation.mutateAsync({
        email,
        password,
        firstName,
        lastName,
      });

      await signIn(response.accessToken, response.expiresInSeconds);
      // Navigate to home page after successful signup
      router.replace("/(app)/home");
    } catch (error) {
      Alert.alert(
        t("auth.signUpFailed"),
        error instanceof Error ? error.message : t("auth.signupError"),
      );
    }
  };

  const handleSocialLogin = (provider: string) => {
    console.log(`Sign up with ${provider}`);
    // TODO: Implement social sign up
  };

  return (
    <SafeAreaView
      style={[styles.container, { backgroundColor: colors.background }]}
    >
      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        style={styles.keyboardView}
      >
        <ScrollView
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          <View style={styles.logoContainer}>
            <Logo width={180} height={27} />
          </View>

          <Text style={[styles.title, { color: colors.text }]}>
            {t("auth.createAccount")}
          </Text>
          <Text style={[styles.subtitle, { color: colors.text }]}>
            {t("auth.signupSubtitle")}
          </Text>

          <View style={styles.form}>
            <Input
              label={t("auth.firstName")}
              placeholder={t("auth.firstNamePlaceholder")}
              value={firstName}
              onChangeText={setFirstName}
              autoCapitalize="words"
              autoComplete="given-name"
            />

            <Input
              label={t("auth.lastName")}
              placeholder={t("auth.lastNamePlaceholder")}
              value={lastName}
              onChangeText={setLastName}
              autoCapitalize="words"
              autoComplete="family-name"
            />

            <Input
              label={t("auth.email")}
              placeholder={t("auth.emailPlaceholder")}
              value={email}
              onChangeText={setEmail}
              keyboardType="email-address"
              autoCapitalize="none"
              autoComplete="email"
            />

            <Input
              label={t("auth.password")}
              placeholder={t("auth.createPasswordPlaceholder")}
              value={password}
              onChangeText={setPassword}
              isPassword
              autoComplete="password-new"
            />

            <Input
              label={t("auth.confirmPassword")}
              placeholder={t("auth.confirmPasswordPlaceholder")}
              value={confirmPassword}
              onChangeText={setConfirmPassword}
              isPassword
              autoComplete="password-new"
            />
          </View>

          <Button
            title={t("auth.createAccount")}
            onPress={handleSignUp}
            loading={signupMutation.isPending}
            containerStyle={styles.createButton}
          />

          {/* <View style={styles.dividerContainer}>
            <View style={styles.divider} />
            <Text style={styles.dividerText}>{t("auth.continueWith")}</Text>
            <View style={styles.divider} />
          </View> */}

          <View style={styles.socialButtons}>
            {/* <SocialButton
              provider="google"
              title={t("auth.googleLogin")}
              onPress={() => handleSocialLogin("google")}
            />
            <SocialButton
              provider="apple"
              title={t("auth.appleLogin")}
              onPress={() => handleSocialLogin("apple")}
            /> */}
            {/* <SocialButton
              provider="wechat"
              title={t("auth.wechatLogin")}
              onPress={() => handleSocialLogin("wechat")}
            /> */}
          </View>

          <View style={styles.footer}>
            <Text style={[styles.footerText, { color: colors.text }]}>
              {t("auth.alreadyHaveAccount")}{" "}
            </Text>
            <Link href="/login" asChild>
              <TouchableOpacity>
                <Text style={styles.linkText}>{t("auth.login")}</Text>
              </TouchableOpacity>
            </Link>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  keyboardView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    paddingHorizontal: 24,
    paddingTop: 20,
    paddingBottom: 40,
  },
  logoContainer: {
    alignItems: "center",
    marginBottom: 32,
  },
  title: {
    fontSize: 28,
    fontWeight: "700",
    textAlign: "center",
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    textAlign: "center",
    marginBottom: 32,
    opacity: 0.7,
  },
  form: {
    marginBottom: 24,
  },
  termsRow: {
    flexDirection: "row",
    alignItems: "flex-start",
    marginTop: 10,
  },
  termsText: {
    flex: 1,
    marginLeft: 8,
    fontSize: 14,
    lineHeight: 20,
  },
  createButton: {
    marginTop: 8,
  },
  dividerContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginVertical: 24,
  },
  divider: {
    flex: 1,
    height: 1,
    backgroundColor: "#e5e7eb",
  },
  dividerText: {
    paddingHorizontal: 16,
    fontSize: 14,
    color: "#6b7280",
  },
  socialButtons: {
    marginBottom: 24,
  },
  footer: {
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
  },
  footerText: {
    fontSize: 14,
  },
  linkText: {
    fontSize: 14,
    color: "#3b82f6",
    fontWeight: "500",
  },
});
