import {
  BottomSheetBackdrop,
  BottomSheetModal,
  BottomSheetTextInput,
  BottomSheetView,
} from "@gorhom/bottom-sheet";
import { FlashList } from "@shopify/flash-list";
import { router, Stack } from "expo-router";
import React, { useCallback, useMemo, useRef, useState } from "react";
import {
  ActivityIndicator,
  Alert,
  RefreshControl,
  StyleSheet,
  TouchableOpacity,
  View,
} from "react-native";
import Toast from "react-native-simple-toast";

import { PatientListSkeleton } from "@/components/skeletons/PatientListSkeleton";
import { ThemedText } from "@/components/ThemedText";
import { Button } from "@/components/ui/Button";
import { Logo } from "@/components/ui/Logo";
import { PatientVisitCard } from "@/components/ui/PatientVisitCard";
import { Colors } from "@/constants/Colors";
import { useLanguage, useTranslation } from "@/context/LanguageContext";
import { useColorScheme } from "@/hooks/useColorScheme";
import { useCards, useCreateCard, useDeleteCard } from "@/lib/api/hooks";
import { formatDateTime } from "@/lib/utils/datetime";
import { MaterialIcons } from "@expo/vector-icons";

// Will be populated in component with translations
let filterOptions: { label: string; value: string }[] = [];

export default function HomeScreen() {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? "light"];
  const { t } = useTranslation();
  const { locale } = useLanguage();
  const [filterValue, setFilterValue] = useState("most_recent");
  const [patientName, setPatientName] = useState("");
  const [selectedCard, setSelectedCard] = useState<{
    id: string;
    name: string;
  } | null>(null);

  // Initialize filter options with translations
  filterOptions = [
    { label: t("home.mostRecent"), value: "most_recent" },
    { label: t("home.oldestFirst"), value: "oldest_first" },
    { label: t("home.nameAsc"), value: "name_asc" },
    { label: t("home.nameDesc"), value: "name_desc" },
  ];

  // API hooks
  const { data: cardsData, isLoading, refetch, isRefetching } = useCards();
  const createPatientMutation = useCreateCard();
  const deleteCardMutation = useDeleteCard();

  // Bottom sheet refs
  const bottomSheetModalRef = useRef<BottomSheetModal>(null);
  const cardOptionsSheetRef = useRef<BottomSheetModal>(null);

  // Snap points for the bottom sheets
  const snapPoints = useMemo(() => ["35%"], []);
  const cardOptionsSnapPoints = useMemo(() => ["25%"], []);

  // Sort patients based on filter
  const sortedCards = useMemo(() => {
    if (!cardsData?.cards) return [];

    const cards = [...cardsData.cards];

    switch (filterValue) {
      case "most_recent":
        return cards.sort(
          (a, b) =>
            new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime(),
        );
      case "oldest_first":
        return cards.sort(
          (a, b) =>
            new Date(a.updatedAt).getTime() - new Date(b.updatedAt).getTime(),
        );
      case "name_asc":
        return cards.sort((a, b) => a.name.localeCompare(b.name));
      case "name_desc":
        return cards.sort((a, b) => b.name.localeCompare(a.name));
      default:
        return cards;
    }
  }, [cardsData?.cards, filterValue, locale]);

  // Backdrop component
  const renderBackdrop = useCallback(
    (props: any) => (
      <BottomSheetBackdrop
        {...props}
        disappearsOnIndex={-1}
        appearsOnIndex={0}
        opacity={0.5}
      />
    ),
    [],
  );

  const handleCardPress = (cardId: string, cardName: string) => {
    router.push(`/cards/${cardId}/files` as const);
  };

  const handleMenuPress = (cardId: string, cardName: string) => {
    setSelectedCard({ id: cardId, name: cardName });
    cardOptionsSheetRef.current?.present();
  };

  const handleNewPatientVisit = useCallback(() => {
    bottomSheetModalRef.current?.present();
  }, []);

  const handleSheetDismiss = useCallback(() => {
    setPatientName("");
  }, []);

  const handleCreatePatient = useCallback(async () => {
    if (patientName.trim()) {
      try {
        const result = await createPatientMutation.mutateAsync({
          name: patientName.trim(),
        });

        // Dismiss bottom sheet
        bottomSheetModalRef.current?.dismiss();

        // Navigate to the new patient's files page
        router.push(`/cards/${result.card.id}/files` as const);
      } catch (error) {
        console.error("Failed to create patient:", error);
        Toast.show(
          t("home.cardCreateFailed") || "Failed to create patient",
          Toast.SHORT,
        );
      }
    }
  }, [patientName, createPatientMutation, t]);

  const handleProfilePress = () => {
    router.push("/(app)/profile");
  };

  const handleRefresh = useCallback(() => {
    refetch();
  }, [refetch]);

  const handleCardOptionsSheetDismiss = useCallback(() => {
    setSelectedCard(null);
  }, []);

  const handleDeleteCard = useCallback(() => {
    if (!selectedCard) return;

    Alert.alert(
      t("home.deleteCardTitle") || "Delete Patient",
      t("home.deleteCardMessage") ||
        "Are you sure you want to delete this patient? This action cannot be undone.",
      [
        {
          text: t("common.cancel") || "Cancel",
          style: "cancel",
        },
        {
          text: t("common.delete") || "Delete",
          style: "destructive",
          onPress: async () => {
            try {
              await deleteCardMutation.mutateAsync(selectedCard.id);
              cardOptionsSheetRef.current?.dismiss();
              Toast.show(
                t("home.cardDeleted") || "Patient deleted successfully",
                Toast.SHORT,
              );
            } catch {
              Toast.show(
                t("home.cardDeleteFailed") || "Failed to delete patient",
                Toast.SHORT,
              );
            }
          },
        },
      ],
      { cancelable: true },
    );
  }, [selectedCard, deleteCardMutation, t]);

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <Stack.Screen
        options={{
          headerShown: true,
          headerTitle: () => <Logo width={150} height={23} />,
          headerRight: () => (
            <TouchableOpacity
              onPress={handleProfilePress}
              style={{ marginRight: 16 }}
            >
              <MaterialIcons
                name="account-circle"
                size={32}
                color={colorScheme === "dark" ? "#e5e7eb" : "#374151"}
              />
            </TouchableOpacity>
          ),
          headerStyle: {
            backgroundColor: colors.background,
          },
          headerShadowVisible: true,
        }}
      />
      <View style={styles.filterContainer}>
        {/* <Dropdown
          options={filterOptions}
          selectedValue={filterValue}
          onValueChange={setFilterValue}
          placeholder={t("home.mostRecent")}
        /> */}
      </View>

      <View style={styles.listContainer}>
        {isLoading ? (
          <PatientListSkeleton count={5} />
        ) : (
          <FlashList
            data={sortedCards}
            renderItem={({ item }) => (
              <PatientVisitCard
                name={item.name}
                dateTime={formatDateTime(item.updatedAt, locale)}
                hasNotification={false}
                onPress={() => handleCardPress(item.id, item.name)}
                onMenuPress={() => handleMenuPress(item.id, item.name)}
              />
            )}
            estimatedItemSize={80}
            contentContainerStyle={styles.listContent}
            showsVerticalScrollIndicator={false}
            extraData={locale}
            ListEmptyComponent={
              <View style={styles.emptyState}>
                <ThemedText type="title" style={styles.emptyStateText}>
                  {t("home.noCards")}
                </ThemedText>
                <ThemedText style={styles.emptyStateSubtext}>
                  {t("home.createFirstCard")}
                </ThemedText>
              </View>
            }
            keyExtractor={(item) => item.id}
            refreshControl={
              <RefreshControl
                refreshing={isRefetching}
                onRefresh={handleRefresh}
                tintColor={colors.tint}
              />
            }
          />
        )}
      </View>

      <View style={styles.buttonContainer}>
        <Button
          title={`+ ${t("home.newCard")}`}
          onPress={handleNewPatientVisit}
          containerStyle={styles.newPatientButton}
        />
      </View>

      <BottomSheetModal
        ref={bottomSheetModalRef}
        index={1}
        snapPoints={snapPoints}
        backdropComponent={renderBackdrop}
        onDismiss={handleSheetDismiss}
        backgroundStyle={{
          backgroundColor: colors.background,
          shadowColor: "#000",
          shadowOffset: {
            width: 0,
            height: 4,
          },
          shadowOpacity: 0.3,
          shadowRadius: 4.65,
          elevation: 8,
        }}
        handleIndicatorStyle={{ backgroundColor: colors.text }}
        enablePanDownToClose
      >
        <BottomSheetView style={styles.sheetContent}>
          <ThemedText type="subtitle" style={styles.sheetTitle}>
            {t("home.newCard")}
          </ThemedText>

          <View style={styles.inputContainer}>
            <ThemedText style={styles.inputLabel}>
              {t("home.patientNameLabel")}
            </ThemedText>
            <BottomSheetTextInput
              value={patientName}
              onChangeText={setPatientName}
              placeholder={t("home.enterPatientName")}
              style={[
                styles.sheetInput,
                {
                  backgroundColor:
                    colorScheme === "dark" ? "#374151" : "#f3f4f6",
                  color: colors.text,
                },
              ]}
              placeholderTextColor={
                colorScheme === "dark" ? "#9ca3af" : "#6b7280"
              }
              autoFocus
            />
          </View>

          <View style={styles.sheetButtons}>
            <Button
              title={t("common.cancel")}
              variant="secondary"
              onPress={() => bottomSheetModalRef.current?.dismiss()}
              containerStyle={styles.cancelButton}
            />
            <Button
              title={t("common.create")}
              onPress={handleCreatePatient}
              disabled={!patientName.trim() || createPatientMutation.isPending}
              loading={createPatientMutation.isPending}
              containerStyle={styles.createButton}
            />
          </View>
        </BottomSheetView>
      </BottomSheetModal>

      <BottomSheetModal
        ref={cardOptionsSheetRef}
        index={0}
        snapPoints={cardOptionsSnapPoints}
        backdropComponent={renderBackdrop}
        onDismiss={handleCardOptionsSheetDismiss}
        backgroundStyle={{ backgroundColor: colors.card }}
        handleIndicatorStyle={{ backgroundColor: colors.icon }}
        enablePanDownToClose
      >
        <BottomSheetView style={styles.sheetContentContainer}>
          <ThemedText type="title" style={styles.optionsSheetTitle}>
            {t("home.cardOptions") || "Patient Options"}
          </ThemedText>
          <ThemedText style={styles.optionsSheetSubtitle}>
            {selectedCard?.name}
          </ThemedText>

          <View style={styles.optionsContainer}>
            <TouchableOpacity
              style={[
                styles.optionItem,
                {
                  backgroundColor:
                    colorScheme === "dark" ? "#7f1d1d" : "#fee2e2",
                },
              ]}
              onPress={handleDeleteCard}
              disabled={deleteCardMutation.isPending}
            >
              <View style={styles.optionContent}>
                <View style={styles.optionHeader}>
                  <MaterialIcons
                    name="delete"
                    size={24}
                    color="#ef4444"
                    style={styles.optionIcon}
                  />
                  <ThemedText
                    type="subtitle"
                    style={[styles.optionTitle, { color: "#ef4444" }]}
                  >
                    {t("home.deleteCard") || "Delete Patient"}
                  </ThemedText>
                </View>
                <ThemedText style={styles.optionDescription}>
                  {t("home.deleteCardDescription") ||
                    "Permanently remove this patient"}
                </ThemedText>
              </View>
              {deleteCardMutation.isPending && (
                <ActivityIndicator size="small" color="#ef4444" />
              )}
            </TouchableOpacity>
          </View>
        </BottomSheetView>
      </BottomSheetModal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  filterContainer: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    alignItems: "flex-end",
  },
  listContainer: {
    flex: 1,
  },
  listContent: {
    paddingHorizontal: 20,
    paddingBottom: 100,
  },
  buttonContainer: {
    position: "absolute",
    bottom: 30,
    left: 20,
    right: 20,
  },
  newPatientButton: {
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  sheetHandle: {
    borderTopLeftRadius: 15,
    borderTopRightRadius: 15,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: -3,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 5,
  },
  sheetContent: {
    flex: 1,
    paddingHorizontal: 20,
    paddingTop: 20,
  },
  sheetTitle: {
    fontSize: 20,
    fontWeight: "600",
    marginBottom: 24,
    textAlign: "center",
  },
  inputContainer: {
    marginBottom: 24,
  },
  inputLabel: {
    fontSize: 14,
    marginBottom: 8,
    opacity: 0.7,
  },
  sheetInput: {
    borderRadius: 12,
    fontSize: 16,
    paddingHorizontal: 16,
    paddingVertical: 12,
    height: 48,
  },
  sheetButtons: {
    flexDirection: "row",
    gap: 12,
  },
  cancelButton: {
    flex: 1,
  },
  createButton: {
    flex: 1,
  },
  emptyState: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingVertical: 60,
  },
  emptyStateText: {
    fontSize: 18,
    fontWeight: "600",
    marginBottom: 8,
  },
  emptyStateSubtext: {
    fontSize: 14,
    opacity: 0.6,
    textAlign: "center",
    paddingHorizontal: 40,
  },
  sheetContentContainer: {
    flex: 1,
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  optionsSheetTitle: {
    fontSize: 20,
    fontWeight: "700",
    marginBottom: 4,
  },
  optionsSheetSubtitle: {
    fontSize: 16,
    opacity: 0.7,
    marginBottom: 24,
  },
  optionsContainer: {
    gap: 12,
  },
  optionItem: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingVertical: 16,
    paddingHorizontal: 20,
    borderRadius: 12,
  },
  optionContent: {
    flex: 1,
  },
  optionHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 4,
  },
  optionIcon: {
    marginRight: 12,
  },
  optionTitle: {
    fontSize: 16,
    fontWeight: "600",
  },
  optionDescription: {
    fontSize: 14,
    opacity: 0.7,
    marginLeft: 36,
  },
});
