import { Ionicons } from "@expo/vector-icons";
import { useBottomTabBarHeight } from "@react-navigation/bottom-tabs";
import Markdown from "@ronradtke/react-native-markdown-display";
import * as Clipboard from "expo-clipboard";
import { useGlobalSearchParams, useNavigation } from "expo-router";
import { fetch } from "expo/fetch";
import React, {
  memo,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import {
  ActivityIndicator,
  Alert,
  Keyboard,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from "react-native";
import Animated, { Easing, FadeIn, FadeOut } from "react-native-reanimated";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import Toast from "react-native-simple-toast";

import { Colors } from "@/constants/Colors";
import { TOKEN_KEY } from "@/context/AuthContext";
import { useTranslation } from "@/context/LanguageContext";
import { useColorScheme } from "@/hooks/useColorScheme";
import {
  useClearChatConversation,
  useConversationMessages,
} from "@/lib/api/hooks";
import * as SecureStore from "expo-secure-store";

interface Message {
  id: string;
  role: "user" | "assistant";
  content: string;
  isStreaming?: boolean;
  error?: boolean;
}

// Memoized message component to prevent unnecessary re-renders
const MessageItem = memo(function MessageItem({
  item,
  renderItem,
}: {
  item: Message;
  renderItem: (props: { item: Message }) => React.ReactElement;
}) {
  return renderItem({ item });
});

export default function AIAssistantScreen() {
  const { id } = useGlobalSearchParams<{ id: string }>();
  const navigation = useNavigation();
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? "light"];
  const { t } = useTranslation();

  const [messages, setMessages] = useState<Message[]>([]);
  const [conversationId, setConversationId] = useState<string | undefined>(
    undefined,
  );
  const [input, setInput] = useState("");
  const [loading, setLoading] = useState(false);
  const [inputFocused, setInputFocused] = useState(false);
  const [isNearBottom, setIsNearBottom] = useState(false); // Start as false to show button on load
  const [composerHeight, setComposerHeight] = useState(60);
  const composerHeightRef = useRef(60);
  const [hasLoadedInitialData, setHasLoadedInitialData] = useState(false);
  const wasNearBottomRef = useRef(true);

  const scrollViewRef = useRef<ScrollView>(null);
  const streamingRef = useRef<boolean>(false);
  const isProgrammaticScrollRef = useRef<boolean>(false);
  const lastScrollTime = useRef<number>(0);
  const scrollMetricsRef = useRef<{
    contentHeight: number;
    scrollPosition: number;
    visibleHeight: number;
  }>({
    contentHeight: 0,
    scrollPosition: 0,
    visibleHeight: 0,
  });

  const tabBarHeight = useBottomTabBarHeight();
  const insets = useSafeAreaInsets();

  // Check if currently near bottom based on scroll metrics
  // This calculates in real-time whether the user is viewing content near the bottom,
  // accounting for the input composer and bottom padding
  const isCurrentlyNearBottom = useCallback(() => {
    const { contentHeight, scrollPosition, visibleHeight } =
      scrollMetricsRef.current;
    const effectiveBottomPadding = composerHeightRef.current + 100; // Composer + disclaimer text
    const distanceFromBottom =
      contentHeight - (scrollPosition + visibleHeight) + effectiveBottomPadding;
    return distanceFromBottom <= effectiveBottomPadding + 20;
  }, []);

  // Markdown styles
  const markdownStyles = useMemo(() => {
    return StyleSheet.create({
      body: {
        color: colors.text,
        fontSize: 16,
        lineHeight: 22,
      },
      heading1: {
        color: colors.text,
        fontSize: 28,
        fontWeight: "bold",
        marginVertical: 8,
      },
      heading2: {
        color: colors.text,
        fontSize: 24,
        fontWeight: "bold",
        marginVertical: 6,
      },
      heading3: {
        color: colors.text,
        fontSize: 20,
        fontWeight: "bold",
        marginVertical: 4,
      },
      strong: {
        fontWeight: "bold",
      },
      em: {
        fontStyle: "italic",
      },
      link: {
        color: colors.tint,
        textDecorationLine: "underline",
      },
      blockquote: {
        backgroundColor: colors.background,
        borderLeftWidth: 4,
        borderLeftColor: colors.tint,
        paddingLeft: 12,
        paddingVertical: 4,
        marginVertical: 8,
      },
      code_inline: {
        backgroundColor: colors.background,
        color: colors.tint,
        paddingHorizontal: 4,
        paddingVertical: 2,
        borderRadius: 4,
        fontFamily: Platform.select({ ios: "Menlo", android: "monospace" }),
        fontSize: 14,
      },
      code_block: {
        backgroundColor: colors.background,
        color: colors.text,
        padding: 12,
        borderRadius: 8,
        marginVertical: 8,
        fontFamily: Platform.select({ ios: "Menlo", android: "monospace" }),
        fontSize: 14,
      },
      fence: {
        backgroundColor: colors.background,
        color: colors.text,
        padding: 12,
        borderRadius: 8,
        marginVertical: 8,
        fontFamily: Platform.select({ ios: "Menlo", android: "monospace" }),
        fontSize: 14,
      },
      list_item: {
        marginVertical: 2,
      },
      bullet_list: {
        marginVertical: 4,
      },
      ordered_list: {
        marginVertical: 4,
      },
      hr: {
        backgroundColor: colors.icon,
        height: 1,
        marginVertical: 16,
      },
      table: {
        borderWidth: 1,
        borderColor: colors.icon,
        marginVertical: 8,
      },
      thead: {
        backgroundColor: colors.background,
      },
      th: {
        borderWidth: 1,
        borderColor: colors.icon,
        padding: 8,
        fontWeight: "bold",
      },
      td: {
        borderWidth: 1,
        borderColor: colors.icon,
        padding: 8,
      },
    });
  }, [colors]);

  // Fetch existing conversation
  const { data: convoData, isLoading: isLoadingHistory } =
    useConversationMessages(id || "");

  // Clear chat mutation
  const clearChatMutation = useClearChatConversation(id || "");

  const handleResetChat = useCallback(() => {
    Alert.alert(
      t("card.resetChatTitle") || "Reset Chat",
      t("card.resetChatMessage") ||
        "Are you sure you want to clear all chat messages? This action cannot be undone.",
      [
        {
          text: t("common.cancel") || "Cancel",
          style: "cancel",
        },
        {
          text: t("card.resetChat") || "Reset",
          style: "destructive",
          onPress: async () => {
            try {
              await clearChatMutation.mutateAsync();
              setMessages([]);
              setConversationId(undefined);
              Toast.show(
                t("card.chatCleared") || "Chat cleared successfully",
                Toast.SHORT,
              );
            } catch (error) {
              console.error("Error clearing chat:", error);
              Alert.alert(
                t("common.error") || "Error",
                t("card.chatClearFailed") || "Failed to clear chat",
              );
            }
          },
        },
      ],
    );
  }, [clearChatMutation, t]);

  // Set navigation options for the parent screen when this tab is focused
  useEffect(() => {
    const unsubscribe = navigation.addListener("focus", () => {
      navigation.getParent()?.setOptions({
        headerRight: () => (
          <TouchableOpacity
            onPress={handleResetChat}
            style={{ paddingLeft: 15 }}
          >
            <Ionicons name="refresh-outline" size={24} color={colors.text} />
          </TouchableOpacity>
        ),
      });
    });

    const unsubscribeBlur = navigation.addListener("blur", () => {
      navigation.getParent()?.setOptions({
        headerRight: undefined,
      });
    });

    // Clean up listeners
    return () => {
      unsubscribe();
      unsubscribeBlur();
    };
  }, [navigation, handleResetChat, colors.text]);

  // Scroll to bottom helper
  const scrollToBottom = useCallback(
    (animated: boolean = true, programmatic: boolean = true) => {
      if (programmatic) {
        isProgrammaticScrollRef.current = true;
      }
      // Use requestAnimationFrame for smooth scrolling
      requestAnimationFrame(() => {
        scrollViewRef.current?.scrollToEnd({ animated });
        // Reset programmatic flag after scroll completes
        if (animated) {
          setTimeout(() => {
            isProgrammaticScrollRef.current = false;
          }, 300);
        } else {
          // For non-animated scrolls, reset immediately
          requestAnimationFrame(() => {
            isProgrammaticScrollRef.current = false;
          });
        }
      });
    },
    [],
  );

  // Add a ref to track if user was near bottom before keyboard opens
  useEffect(() => {
    wasNearBottomRef.current = isNearBottom;
  }, [isNearBottom]);

  // Handle keyboard appearance to maintain scroll position
  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener(
      "keyboardDidShow",
      () => {
        // If the user was already near the bottom, scroll to keep them there.
        // This makes the UI feel more responsive, as the content adjusts
        // with the keyboard's appearance.
        if (wasNearBottomRef.current) {
          scrollToBottom(true, true);
        }
      },
    );

    return () => {
      keyboardDidShowListener.remove();
    };
  }, [scrollToBottom]);

  // Track scroll position to decide when to show the button
  const handleScroll = useCallback((event: any) => {
    const now = Date.now();
    const { contentOffset, contentSize, layoutMeasurement } = event.nativeEvent;
    const currentContentHeight = contentSize.height;
    const scrollPosition = contentOffset.y;
    const visibleHeight = layoutMeasurement.height;

    // Account for the composer height and some padding when calculating distance from bottom
    // The "visible bottom" for the user is above the input box
    const effectiveBottomPadding = composerHeightRef.current + 100; // composer + padding + disclaimer
    const distanceFromBottom =
      currentContentHeight -
      (scrollPosition + visibleHeight) +
      effectiveBottomPadding;

    // Use smaller threshold for more responsive detection
    const nearEnd = distanceFromBottom <= effectiveBottomPadding + 20;
    setIsNearBottom(nearEnd);

    // Update scroll metrics
    scrollMetricsRef.current = {
      contentHeight: currentContentHeight,
      scrollPosition: scrollPosition,
      visibleHeight: visibleHeight,
    };

    // Only debounce for other scroll processing
    if (now - lastScrollTime.current < 50) {
      return; // Debounce rapid scroll events
    }
    lastScrollTime.current = now;
  }, []);

  // Map backend messages to chat UI once fetched
  useEffect(() => {
    if (!convoData) return;

    const mapped: Message[] = [];
    convoData.messages.forEach((pair, idx) => {
      mapped.push({
        id: `${idx}-q`,
        role: "user",
        content: pair.query,
      });
      mapped.push({
        id: `${idx}-a`,
        role: "assistant",
        content: pair.answer,
      });
    });

    setMessages(mapped);
    setConversationId(convoData.conversationId);
    setHasLoadedInitialData(true);

    // When messages first load, scroll to bottom after a delay
    if (mapped.length > 0) {
      setTimeout(() => {
        scrollToBottom(true, true);
      }, 200);
    }
  }, [convoData, scrollToBottom]);

  // Mark as loaded if we've checked and there's no conversation
  useEffect(() => {
    if (!isLoadingHistory && convoData === undefined) {
      setHasLoadedInitialData(true);
    }
  }, [isLoadingHistory, convoData]);

  const API_URL = process.env.EXPO_PUBLIC_API_DOMAIN || "http://localhost:3000";

  const copyToClipboard = useCallback(
    async (text: string) => {
      await Clipboard.setStringAsync(text);
      Toast.show(t("common.copied") || "Copied to clipboard", Toast.SHORT);
    },
    [t],
  );

  const sendMessage = async () => {
    const trimmed = input.trim();
    if (!trimmed || loading) return;

    // Dismiss keyboard
    Keyboard.dismiss();

    const userMsg: Message = {
      id: Date.now().toString(),
      role: "user",
      content: trimmed,
    };

    // Add user message
    setMessages((prev) => [...prev, userMsg]);
    setInput("");
    setLoading(true);

    // Placeholder assistant message
    const placeholderId = `${Date.now()}-assistant`;
    let currentContent = "";
    streamingRef.current = true;

    // ALWAYS scroll to bottom after sending message
    scrollToBottom(true);

    setMessages((prev) => [
      ...prev,
      {
        id: placeholderId,
        role: "assistant",
        content: "",
        isStreaming: true,
      },
    ]);

    try {
      // Get auth token
      const token = await SecureStore.getItemAsync(TOKEN_KEY);

      const response = await fetch(
        `${API_URL}/v1/card/${id}/llm/chat-message/stream`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Accept: "text/event-stream",
            ...(token && { Authorization: `Bearer ${token}` }),
          },
          body: JSON.stringify({
            query: trimmed,
            conversation_id: conversationId,
          }),
        },
      );

      if (!response.ok) {
        const errorText = await response.text();
        console.error("Response error:", response.status, errorText);
        throw new Error(`Request failed: ${response.status}`);
      }

      const reader = response.body?.getReader();
      const decoder = new TextDecoder();

      if (!reader) {
        throw new Error("No response body");
      }

      let buffer = "";
      let newConversationId = conversationId;

      while (streamingRef.current) {
        const { done, value } = await reader.read();
        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split("\n");
        buffer = lines.pop() || "";

        for (const line of lines) {
          if (line.startsWith("data: ")) {
            const dataStr = line.slice(6);
            if (dataStr === "[DONE]") {
              streamingRef.current = false;
              break;
            }

            if (dataStr.trim()) {
              try {
                const data = JSON.parse(dataStr);

                if (data.event === "message") {
                  // Append message chunk
                  currentContent += data.data || "";
                  setMessages((prev) =>
                    prev.map((m) =>
                      m.id === placeholderId
                        ? { ...m, content: currentContent, isStreaming: true }
                        : m,
                    ),
                  );

                  if (data.conversation_id) {
                    newConversationId = data.conversation_id;
                  }

                  // Auto-scroll during streaming only if user is near bottom
                  // This prevents interrupting users who are reading previous messages
                  requestAnimationFrame(() => {
                    if (streamingRef.current && isCurrentlyNearBottom()) {
                      scrollToBottom(false, true);
                    }
                  });
                } else if (data.event === "message_end") {
                  // Final message
                  const finalContent = data.answer || currentContent;
                  setMessages((prev) =>
                    prev.map((m) =>
                      m.id === placeholderId
                        ? { ...m, content: finalContent, isStreaming: false }
                        : m,
                    ),
                  );

                  if (data.conversation_id) {
                    setConversationId(data.conversation_id);
                  }
                  streamingRef.current = false;
                } else if (data.event === "error") {
                  throw new Error(data.data || "Stream error");
                }
              } catch (e) {
                console.error("Error parsing SSE data:", e, dataStr);
              }
            }
          }
        }
      }

      if (newConversationId && newConversationId !== conversationId) {
        setConversationId(newConversationId);
      }
    } catch (err) {
      console.error("Streaming error:", err);
      streamingRef.current = false;

      setMessages((prev) =>
        prev.map((m) =>
          m.id === placeholderId
            ? {
                ...m,
                content: t("card.aiErrorMessage"),
                error: true,
                isStreaming: false,
              }
            : m,
        ),
      );

      Alert.alert(
        t("card.aiConnectionErrorTitle"),
        t("card.aiConnectionErrorMessage"),
      );
    } finally {
      setLoading(false);
      streamingRef.current = false;
    }
  };

  const renderItem = useCallback(
    ({ item }: { item: Message }) => {
      const isUser = item.role === "user";

      // User message keeps bubble, AI message is plain
      if (isUser) {
        return (
          <Animated.View entering={FadeIn}>
            <View
              style={[
                styles.bubble,
                {
                  backgroundColor: colors.tint,
                  alignSelf: "flex-end",
                },
              ]}
            >
              {item.content ? (
                <Text
                  style={{
                    color: colors.background,
                    fontSize: 16,
                    lineHeight: 22,
                  }}
                >
                  {item.content}
                </Text>
              ) : (
                <ActivityIndicator color={colors.background} />
              )}
            </View>
          </Animated.View>
        );
      }

      // AI assistant message (no bubble)
      return (
        <Animated.View entering={FadeIn} style={styles.aiMessage}>
          {item.content || item.isStreaming ? (
            <>
              <Markdown style={markdownStyles}>{item.content}</Markdown>
              {item.isStreaming && (
                <Text style={[styles.streamingCursor, { color: colors.text }]}>
                  ▊
                </Text>
              )}
              {!item.isStreaming && item.content && (
                <TouchableOpacity
                  style={[styles.copyButton, { borderColor: colors.icon }]}
                  onPress={() => copyToClipboard(item.content)}
                >
                  <Ionicons name="copy-outline" size={16} color={colors.icon} />
                  <Text style={[styles.copyButtonText, { color: colors.icon }]}>
                    {t("common.copy") || "Copy"}
                  </Text>
                </TouchableOpacity>
              )}
            </>
          ) : (
            <ActivityIndicator color={colors.text} />
          )}
        </Animated.View>
      );
    },
    [colors, markdownStyles, copyToClipboard, t],
  );

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === "ios" ? "padding" : "height"}
      keyboardVerticalOffset={Platform.OS === "ios" ? 15 : tabBarHeight + 25}
    >
      <View style={styles.flex1}>
        {isLoadingHistory ||
        (!hasLoadedInitialData && messages.length === 0) ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={colors.text} />
            <Text style={[styles.loadingText, { color: colors.text }]}>
              {t("common.loadingConversation")}
            </Text>
          </View>
        ) : (
          <ScrollView
            ref={scrollViewRef}
            onScroll={handleScroll}
            scrollEventThrottle={16}
            keyboardDismissMode="interactive"
            keyboardShouldPersistTaps="handled"
            showsVerticalScrollIndicator={false}
            contentContainerStyle={{
              ...styles.messageList,
              paddingBottom: 20, // Small padding for visual spacing
              flexGrow: 1, // Ensure ScrollView fills available space when content is small
            }}
          >
            {messages.length === 0 ? (
              <View style={styles.emptyContainer}>
                <Ionicons
                  name="chatbubbles-outline"
                  size={48}
                  color={colors.icon}
                />
                <Text style={[styles.emptyText, { color: colors.text }]}>
                  {t("card.startConversation")}
                </Text>
              </View>
            ) : (
              messages.map((item) => (
                <MessageItem
                  key={item.id}
                  item={item}
                  renderItem={renderItem}
                />
              ))
            )}
          </ScrollView>
        )}

        {/* Composer and scroll button container */}
        <View style={styles.bottomContainer}>
          {/* Scroll-to-bottom button */}
          {!isNearBottom && messages.length > 0 && (
            <Animated.View
              entering={FadeIn.duration(200).easing(Easing.inOut(Easing.ease))}
              exiting={FadeOut.duration(100).easing(Easing.inOut(Easing.ease))}
              style={[
                styles.scrollToBottomButton,
                {
                  backgroundColor: colors.card,
                  borderColor: colors.icon,
                  shadowOpacity: colorScheme === "dark" ? 0 : 0.2,
                  bottom: composerHeight + 10,
                },
              ]}
            >
              <TouchableOpacity
                style={styles.scrollToBottomTouchable}
                onPress={() => {
                  // Force scroll to bottom
                  isProgrammaticScrollRef.current = true;
                  scrollViewRef.current?.scrollToEnd({ animated: true });
                  // Reset flag after animation
                  setTimeout(() => {
                    isProgrammaticScrollRef.current = false;
                  }, 300);
                }}
                activeOpacity={0.8}
              >
                <Ionicons name="chevron-down" size={24} color={colors.text} />
              </TouchableOpacity>
            </Animated.View>
          )}

          {/* Composer */}
          <View
            onLayout={(event) => {
              const { height } = event.nativeEvent.layout;
              setComposerHeight(height);
              composerHeightRef.current = height;
            }}
            style={[
              styles.composerContainer,
              {
                backgroundColor: colors.card,
                borderColor: inputFocused ? colors.tint : colors.icon,
                shadowOpacity: colorScheme === "dark" ? 0 : 0.1,
                paddingBottom: Platform.OS === "ios" ? 8 : insets.bottom || 8,
              },
            ]}
          >
            <TextInput
              style={[styles.input, { color: colors.text }]}
              placeholder={t("card.askAboutCard")}
              placeholderTextColor={colors.icon}
              multiline
              maxLength={1000}
              value={input}
              onChangeText={setInput}
              onFocus={() => setInputFocused(true)}
              onBlur={() => setInputFocused(false)}
              editable={!loading}
              returnKeyType="send"
              onSubmitEditing={sendMessage}
            />
            <TouchableOpacity
              style={[
                styles.sendButton,
                {
                  backgroundColor:
                    input.trim() && !loading ? colors.tint : colors.icon,
                },
                (!input.trim() || loading) && styles.sendButtonDisabled,
              ]}
              onPress={sendMessage}
              disabled={!input.trim() || loading}
              activeOpacity={0.7}
            >
              {loading ? (
                <ActivityIndicator size="small" color={colors.background} />
              ) : (
                <Ionicons
                  name="arrow-up"
                  size={22}
                  color={colors.background}
                  style={styles.sendIcon}
                />
              )}
            </TouchableOpacity>
          </View>
        </View>

        {/* AI Disclaimer */}
        <Text
          style={[
            styles.disclaimer,
            {
              color: colors.text,
              marginBottom: Platform.OS === "ios" ? tabBarHeight : 0,
            },
          ]}
        >
          {t("card.aiDisclaimer")}
        </Text>
      </View>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  flex1: {
    flex: 1,
    marginBottom: 10,
  },
  messageList: {
    padding: 12,
  },
  bubble: {
    maxWidth: "80%",
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    borderBottomLeftRadius: 16,
    borderBottomRightRadius: 4,
    paddingVertical: 8,
    paddingHorizontal: 12,
    marginVertical: 4,
  },
  aiMessage: {
    alignSelf: "flex-start",
    marginVertical: 4,
    paddingHorizontal: 8,
    maxWidth: "100%",
  },
  composerContainer: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 8,
    paddingVertical: 8,
    borderWidth: 1,
    borderRadius: 24,
    marginHorizontal: 15,
    // width: "100%",
    shadowColor: "#000",
    shadowRadius: 8,
    shadowOffset: { width: 0, height: 2 },
    elevation: 3, // Android shadow
  },
  input: {
    flex: 1,
    minHeight: 40,
    maxHeight: 120,
    fontSize: 16,
    paddingHorizontal: 12,
    paddingVertical: 8,
  },
  sendButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: "center",
    alignItems: "center",
    marginLeft: 8,
    marginRight: 4,
    // Add subtle shadow for depth
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 3,
  },
  sendButtonDisabled: {
    opacity: 0.4,
    shadowOpacity: 0,
    elevation: 0,
  },
  sendIcon: {
    marginLeft: 1, // Slight adjustment to center the arrow
    marginBottom: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingTop: 100,
    minHeight: 400, // Ensure minimum height for proper centering in ScrollView
  },
  emptyText: {
    marginTop: 16,
    fontSize: 16,
    opacity: 0.7,
  },
  disclaimer: {
    fontSize: 10,
    marginTop: 4,
    textAlign: "center",
    marginBottom: Platform.OS === "ios" ? 20 : 12,
    marginHorizontal: 20,
    opacity: 0.6,
  },
  bottomContainer: {
    position: "relative",
  },
  scrollToBottomButton: {
    position: "absolute",
    right: 20,
    width: 44,
    height: 44,
    borderRadius: 22,
    borderWidth: 1,
    shadowColor: "#000",
    shadowRadius: 4,
    shadowOffset: { width: 0, height: 2 },
    elevation: 3,
    zIndex: 10,
  },
  scrollToBottomTouchable: {
    width: "100%",
    height: "100%",
    justifyContent: "center",
    alignItems: "center",
  },
  copyButton: {
    flexDirection: "row",
    alignItems: "center",
    marginTop: 8,
    paddingVertical: 4,
    paddingHorizontal: 8,
    borderWidth: 1,
    borderRadius: 8,
    alignSelf: "flex-start",
  },
  copyButtonText: {
    fontSize: 14,
    marginLeft: 4,
  },
  streamingCursor: {
    opacity: 0.5,
  },
});
