export default () => {
  return {
    expo: {
      name: process.env.APP_ENV === "production" ? "Scribe" : "Scribe Beta",
      slug: "scribe-app",
      version: "0.1.3",
      orientation: "portrait",
      icon: "./assets/images/app-icon.png",
      scheme: "scribeapp",
      userInterfaceStyle: "automatic",
      newArchEnabled: true,
      ios: {
        supportsTablet: true,
        config: {
          usesNonExemptEncryption: false,
        },
        bundleIdentifier:
          process.env.APP_ENV === "production"
            ? "cn.com.aimoves.app"
            : "cn.com.aimoves.scribebeta",
        googleServicesFile: "./google/ios-beta.plist",
      },
      android: {
        adaptiveIcon: {
          foregroundImage: "./assets/images/adaptive-icon.png",
          backgroundColor: "#ffffff",
        },
        edgeToEdgeEnabled: true,
        package:
          process.env.APP_ENV === "production"
            ? "cn.com.aimoves.app"
            : "cn.com.aimoves.scribebeta",
        permissions: [
          "android.permission.RECORD_AUDIO",
          "android.permission.MODIFY_AUDIO_SETTINGS",
        ],
      },
      web: {
        bundler: "metro",
        output: "static",
        favicon: "./assets/images/favicon.png",
      },
      plugins: [
        "expo-router",
        [
          "expo-splash-screen",
          {
            image: "./assets/images/splash-icon-dark.png",
            imageWidth: 200,
            resizeMode: "contain",
            backgroundColor: "#ffffff",
            dark: {
              image: "./assets/images/splash-icon-light.png",
              backgroundColor: "#1a1a1a",
            },
          },
        ],
        [
          "expo-secure-store",
          {
            configureAndroidBackup: true,
          },
        ],
        [
          "expo-audio",
          {
            microphonePermission:
              "Allow $(PRODUCT_NAME) to access your microphone.",
          },
        ],
        "expo-localization",
        [
          "@siteed/expo-audio-studio",
          {
            enablePhoneStateHandling: false,
            iosBackgroundModes: {
              useAudio: true,
              useProcessing: true,
            },
            iosConfig: {
              backgroundProcessingTitle: "Audio Recording",
            },
          },
        ],
        [
          "@react-native-google-signin/google-signin",
          {
            iosUrlScheme:
              "119719605721-d3ifi0286stcbph9pdjem78n1rqod26b.apps.googleusercontent.com",
          },
        ],
      ],
      experiments: {
        typedRoutes: true,
      },
      extra: {
        router: {},
        eas: {
          projectId: "cb01c2c0-ee4d-4cb9-869c-13eb0e9c84ac",
        },
      },
    },
  };
};
